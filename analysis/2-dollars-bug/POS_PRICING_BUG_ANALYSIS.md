# POS Pricing Bug Analysis & Resolution Plan

## 🐛 **Bug Description**
Every transaction processed through the POS (Point of Sale) system is charged exactly **$2.00** regardless of the station's configured pricing or any customizations that have been set.

## 🔍 **Investigation Summary**

### **Key Findings**

1. **Missing Order Creation Logic**: The admin interface has no API endpoints for order creation or payment processing
2. **External Transaction Processing**: POS transactions are likely handled by the Carku API or external system
3. **Read-Only Order Management**: Current system only displays orders, doesn't create them
4. **Potential Configuration Sync Issue**: Station pricing might not be properly synced to POS hardware

### **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   Carku API     │    │  POS Hardware   │
│  (This Repo)    │    │  (External)     │    │   (Physical)    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Station Config│───▶│ • Transaction   │───▶│ • Payment       │
│ • Pricing Setup │    │   Processing    │    │   Processing    │
│ • Order Display │◀───│ • Hardware Comm │◀───│ • User Interface│
│ • Read-Only     │    │ • Price Sync?   │    │ • $2 Bug Here?  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

```mermaid
sequenceDiagram
    participant Admin as Admin Panel (This Repo)
    participant API as Carku API (External)
    participant POS as POS Hardware (Physical)

    Admin->>API: Station Config
    Admin->>API: Pricing Setup
    API-->>Admin: Order Display
    Admin->>API: Read Only

    API->>POS: Transaction Processing
    API->>POS: Hardware Comm
    API->>POS: Price Sync?

    POS-->>API: User Interface
    POS->>API: Payment Processing
    POS->>API: $2 Bug Here?
```

## 📋 **Components & Views Involved**

### **1. Station Configuration**
- **File**: `pages/organizations/[organization_id]/stations/[id]/settings.vue`
- **Purpose**: Configure station pricing (`hourly_price_cents`)
- **POS Reference**: Contains explicit mention of POS behavior for free stations

### **2. Order Display Components**
- **File**: `components/OrderTable.vue`
- **Purpose**: Display transaction history with `amount_charged_cents`
- **Usage**: Used in station and user order pages

### **3. Database Schema**
- **File**: `types/database.types.ts`
- **Key Fields**:
  - `amount_charged_cents`: Final charged amount (where $2 bug appears)
  - `hourly_price_cents`: Station's configured rate
  - `stripe_payment_intent_id`: Payment processing reference

### **4. Carku API Integration**
- **File**: `server/services/carku.ts`
- **Purpose**: Communication with battery station hardware
- **Key Operations**:
  - `popup_confirm`: POS transaction confirmation
  - `rent_confirm`: Rental payment confirmation
  - `sync_setting`: Configuration synchronization

## 🎯 **Root Cause Analysis**

### **Most Likely Causes**

1. **Hardcoded Value in Carku API**
   - POS operations (`popup_confirm`, `rent_confirm`) return hardcoded $2
   - External system not using station configuration

2. **Missing Price Synchronization**
   - Station pricing updates don't trigger `sync_setting` to hardware
   - POS hardware uses default/fallback pricing

3. **External Payment Processing**
   - Separate payment service with hardcoded values
   - Not integrated with admin panel pricing configuration

### **Evidence Supporting External Bug**

- No order creation endpoints in admin interface
- Orders appear in database but creation logic is elsewhere
- POS reference in station settings suggests external integration
- Carku API handles hardware communication

## 🧪 **Test Case Created**

**File**: `tests/pos-pricing-bug.test.ts`

The test suite includes:
- ✅ Pricing calculation validation
- ✅ Bug reproduction scenarios
- ✅ Carku API integration tests
- ✅ Expected behavior after fix

**Run Tests**:
```bash
npm test tests/pos-pricing-bug.test.ts
```

## 🔧 **Resolution Plan**

### **Phase 1: Investigation & Verification**

1. **Verify Bug Existence**
   - Test POS transactions with different station configurations
   - Confirm $2 charge appears regardless of settings
   - Document specific scenarios where bug occurs

2. **Identify Transaction Flow**
   - Trace how POS transactions are processed
   - Identify where pricing is determined
   - Map data flow from POS → Carku API → Database

### **Phase 2: Root Cause Identification**

1. **Check Carku API Integration**
   - Examine `popup_confirm` and `rent_confirm` responses
   - Verify if pricing data is sent to Carku API
   - Test `sync_setting` operation for price updates

2. **Investigate Missing Sync**
   - Check if station price updates trigger hardware sync
   - Verify Carku API receives current pricing configuration
   - Test hardware configuration update process

### **Phase 3: Implementation**

**Option A: Fix Missing Sync**
```typescript
// In station settings update
const { mutate: applyChanges } = useMutation({
  mutationFn: async () => {
    // Update database
    await supabase.from("stations").update({
      hourly_price_cents: hourlyPrice.value * 100,
      is_free: isFreeToUse.value,
    })

    // 🔧 ADD: Sync to hardware
    await $fetch("/api/stations/handler", {
      method: "POST",
      body: {
        did: stationId,
        hourly_price_cents: hourlyPrice.value * 100,
        is_free: isFreeToUse.value
      },
      query: { option: "sync_setting" }
    })
  }
})
```

**Option B: Fix Carku API Integration**
- Update Carku API calls to include pricing data
- Ensure POS operations receive current station configuration
- Implement proper error handling for pricing sync failures

**Option C: Implement Order Creation API**
- Create order creation endpoints in admin interface
- Handle payment processing with correct pricing
- Integrate with existing Stripe payment system

### **Phase 4: Testing & Validation**

1. **Unit Tests**: Run created test suite
2. **Integration Tests**: Test with actual POS hardware
3. **User Acceptance**: Verify different pricing scenarios work correctly

## 📝 **Next Steps**

1. **Immediate**: Run the created test suite to validate pricing logic
2. **Investigation**: Test actual POS transactions to confirm bug
3. **Development**: Implement price synchronization fix
4. **Testing**: Validate fix with multiple pricing configurations
5. **Deployment**: Roll out fix and monitor transaction amounts

## 🚨 **Critical Questions to Answer**

1. **Where exactly does the $2 value come from?**
   - Carku API response?
   - Hardware default?
   - External payment service?

2. **Is pricing sync implemented?**
   - Does `sync_setting` get called on price updates?
   - Does hardware receive updated configuration?

3. **What triggers order creation?**
   - POS hardware directly?
   - Carku API webhook?
   - External service?

## 📞 **Recommended Actions**

1. **Test the bug reproduction** using the created test suite
2. **Monitor Carku API calls** during POS transactions
3. **Check database logs** for order creation patterns
4. **Implement price sync** in station settings update
5. **Add logging** to track pricing data flow

---

*This analysis provides a comprehensive foundation for resolving the POS pricing bug. The test suite and investigation plan should help identify the exact root cause and guide the implementation of a proper fix.*
