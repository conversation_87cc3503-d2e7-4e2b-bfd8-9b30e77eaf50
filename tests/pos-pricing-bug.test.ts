import { beforeEach, describe, expect, it, vi } from "vitest";

import { apiRequest } from "~/server/services/carku";

// Mock the Carku API
vi.mock("~/server/services/carku", () => ({
  apiRequest: vi.fn(),
}));

describe("POS Pricing Bug Investigation", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Station Pricing Configuration", () => {
    it("should store hourly price in cents correctly", () => {
      const hourlyPriceDollars = 5.5;
      const expectedCents = 550;

      const result = hourlyPriceDollars * 100;
      expect(result).toBe(expectedCents);
    });

    it("should handle free station configuration", () => {
      const isFreeToUse = true;
      const hourlyPrice = 0;

      expect(isFreeToUse).toBe(true);
      expect(hourlyPrice).toBe(0);
    });
  });

  describe("Order Amount Calculation", () => {
    it("should calculate correct amount for 1 hour rental", () => {
      const hourlyPriceCents = 550; // $5.50
      const durationHours = 1;
      const expectedAmount = 550;

      const calculatedAmount = hourlyPriceCents * durationHours;
      expect(calculatedAmount).toBe(expectedAmount);
    });

    it("should calculate correct amount for partial hour rental", () => {
      const hourlyPriceCents = 550; // $5.50
      const durationMinutes = 30;
      const expectedAmount = 275; // $2.75 for 30 minutes

      const calculatedAmount = Math.round(
        (hourlyPriceCents * durationMinutes) / 60,
      );
      expect(calculatedAmount).toBe(expectedAmount);
    });

    it("should NOT default to $2 (200 cents) for any configuration", () => {
      const buggyAmount = 200; // The reported $2 bug
      const hourlyPriceCents = 550; // $5.50 configured price
      const durationMinutes = 30;

      const correctAmount = Math.round(
        (hourlyPriceCents * durationMinutes) / 60,
      );

      // This test should fail if the bug exists
      expect(correctAmount).not.toBe(buggyAmount);
      expect(correctAmount).toBe(275); // Correct amount for 30 min at $5.50/hour
    });
  });

  describe("Carku API Integration", () => {
    it("should call sync_setting when station pricing is updated", async () => {
      const mockApiRequest = vi.mocked(apiRequest);
      mockApiRequest.mockResolvedValue({
        data: { success: true },
        code: 200,
        msg: "OK",
      });

      const stationId = "station-123";
      const newPriceCents = 550;

      // Simulate the API call that should happen when pricing is updated
      await apiRequest("sync_setting", {
        did: stationId,
        hourly_price_cents: newPriceCents,
      });

      expect(mockApiRequest).toHaveBeenCalledWith("sync_setting", {
        did: stationId,
        hourly_price_cents: newPriceCents,
      });
    });

    it("should handle POS confirmation with correct pricing", async () => {
      const mockApiRequest = vi.mocked(apiRequest);
      mockApiRequest.mockResolvedValue({
        data: {
          amount_charged_cents: 275, // Should be calculated amount, not hardcoded $2
          success: true,
        },
        code: 200,
        msg: "OK",
      });

      const stationId = "station-123";
      const slotId = 1;
      const expectedAmount = 275; // $2.75 for 30 min at $5.50/hour

      const response = await apiRequest("popup_confirm", {
        did: stationId,
        slot: slotId,
      });

      expect(response.data.amount_charged_cents).toBe(expectedAmount);
      expect(response.data.amount_charged_cents).not.toBe(200); // Should not be $2
    });
  });

  describe("Bug Reproduction Scenarios", () => {
    it("should reproduce the $2 bug scenario", async () => {
      // This test simulates the reported bug where POS always charges $2
      const mockApiRequest = vi.mocked(apiRequest);

      // Station is configured with $5.50/hour (assuming this is unused)
      // But POS returns hardcoded $2 (the bug)
      mockApiRequest.mockResolvedValue({
        data: {
          amount_charged_cents: 200, // Hardcoded $2 - this is the bug!
          success: true,
        },
        code: 200,
        msg: "OK",
      });

      const response = await apiRequest("popup_confirm", {
        did: "station-123",
        slot: 1,
      });

      // This assertion will pass if the bug exists
      let responseData = response.data as { amount_charged_cents: number };
      expect(responseData.amount_charged_cents).toBe(200);

      // Log the issue for debugging
      if (responseData.amount_charged_cents === 200) {
        console.warn(
          "🐛 BUG DETECTED: POS is returning hardcoded $2 instead of configured pricing",
        );
      }
    });

    it("should test different station configurations with POS", async () => {
      const testCases = [
        { hourlyPrice: 100, description: "$1.00/hour" },
        { hourlyPrice: 300, description: "$3.00/hour" },
        { hourlyPrice: 550, description: "$5.50/hour" },
        { hourlyPrice: 1000, description: "$10.00/hour" },
      ];

      const mockApiRequest = vi.mocked(apiRequest);

      for (const testCase of testCases) {
        // Mock the bug: always returns $2 regardless of configuration
        mockApiRequest.mockResolvedValue({
          data: {
            amount_charged_cents: 200, // Always $2 - this is the bug!
            success: true,
          },
          code: 200,
          msg: "OK",
        });

        const response = await apiRequest("popup_confirm", {
          did: "station-123",
          slot: 1,
          hourly_price_cents: testCase.hourlyPrice,
        });

        // All these will fail if the bug is fixed
        let responseData = response.data as { amount_charged_cents: number };
        expect(responseData.amount_charged_cents).toBe(200);
        console.log(
          `${testCase.description}: Got $${responseData.amount_charged_cents / 100} (should be dynamic)`,
        );
      }
    });

    it("should demonstrate expected behavior after fix", async () => {
      const mockApiRequest = vi.mocked(apiRequest);

      // After fix: POS should use actual station configuration
      const stationPriceCents = 550; // $5.50/hour
      const durationMinutes = 30;
      const expectedAmount = Math.round(
        (stationPriceCents * durationMinutes) / 60,
      ); // $2.75

      mockApiRequest.mockResolvedValue({
        data: {
          amount_charged_cents: expectedAmount, // Dynamic pricing - this is correct!
          success: true,
        },
        code: 200,
        msg: "OK",
      });

      const response = await apiRequest("popup_confirm", {
        did: "station-123",
        slot: 1,
        hourly_price_cents: stationPriceCents,
        duration_minutes: durationMinutes,
      });

      let responseData = response.data as { amount_charged_cents: number };
      expect(responseData.amount_charged_cents).toBe(275); // $2.75
      expect(responseData.amount_charged_cents).not.toBe(200); // Not hardcoded $2
    });
  });
});
